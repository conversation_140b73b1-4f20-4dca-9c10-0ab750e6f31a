#!/usr/bin/env python3
"""
Test script for parent-child relationship handling in discovery search.

This script tests the functionality where child documents (with additionalType ending in 'Aggregation')
are replaced with their parent documents in search results.
"""

import requests
import json
from typing import List, Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000"  # Adjust as needed

def test_parent_child_relationship_handling():
    """Test that child documents are replaced with parent documents in search results"""
    
    print("🔍 Testing Parent-Child Relationship Handling")
    print("=" * 50)
    
    # Test cases that should return child documents which should be replaced with parents
    test_cases = [
        {
            "name": "Search for TimeSeriesAggregation (child type)",
            "params": {"contentType": "TimeSeriesAggregation", "pageSize": 10},
            "description": "Should find child docs and replace with parents"
        },
        {
            "name": "Search for GeographicFeatureAggregation (child type)",
            "params": {"contentType": "GeographicFeatureAggregation", "pageSize": 10},
            "description": "Should find child docs and replace with parents"
        },
        {
            "name": "Search for MultidimensionalAggregation (child type)",
            "params": {"contentType": "MultidimensionalAggregation", "pageSize": 10},
            "description": "Should find child docs and replace with parents"
        },
        {
            "name": "General search that might include child docs",
            "params": {"term": "water", "pageSize": 20},
            "description": "Should replace any child docs found with parents"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}")
        print(f"   {test_case['description']}")
        
        try:
            response = requests.get(
                f"{API_BASE_URL}/api/discovery/search",
                params=test_case["params"],
                timeout=30
            )
            
            if response.status_code == 200:
                results = response.json()
                
                if isinstance(results, list):
                    records = results
                else:
                    records = results.get("results", [])
                
                print(f"   ✅ Found {len(records)} records")
                
                # Analyze the results
                analyze_parent_child_results(records, test_case["name"])
                
            else:
                print(f"   ❌ Search failed with status {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Test failed: {e}")


def analyze_parent_child_results(records: List[Dict[str, Any]], test_name: str):
    """Analyze search results to verify parent-child relationship handling"""
    
    child_docs_found = []
    parent_docs_found = []
    docs_with_ispartof = []
    
    for record in records:
        additional_type = record.get("additionalType", "")
        is_part_of = record.get("isPartOf", [])
        identifier = record.get("identifier", [])
        
        # Check if this looks like a child document
        if isinstance(additional_type, str) and additional_type.endswith("Aggregation"):
            child_docs_found.append({
                "name": record.get("name", "Unknown"),
                "additionalType": additional_type,
                "id": record.get("_id", "Unknown"),
                "isPartOf": is_part_of
            })
        
        # Check if this looks like a parent document
        if identifier and len(identifier) > 0:
            parent_docs_found.append({
                "name": record.get("name", "Unknown"),
                "additionalType": additional_type,
                "id": record.get("_id", "Unknown"),
                "identifier": identifier
            })
        
        # Track documents with isPartOf field
        if is_part_of and len(is_part_of) > 0:
            docs_with_ispartof.append({
                "name": record.get("name", "Unknown"),
                "additionalType": additional_type,
                "isPartOf": is_part_of
            })
    
    # Report findings
    print(f"   📊 Analysis for {test_name}:")
    print(f"      - Child documents found: {len(child_docs_found)}")
    print(f"      - Parent documents found: {len(parent_docs_found)}")
    print(f"      - Documents with isPartOf: {len(docs_with_ispartof)}")
    
    # If we found child documents, this might indicate the replacement didn't work
    if child_docs_found:
        print(f"   ⚠️  WARNING: Found {len(child_docs_found)} child documents in results")
        print("      These should have been replaced with parent documents:")
        for child in child_docs_found[:3]:  # Show first 3
            print(f"        - {child['name']} (Type: {child['additionalType']})")
    else:
        print("   ✅ No child documents found in results (good - they should be replaced)")
    
    # Show some parent documents if found
    if parent_docs_found:
        print(f"   📄 Sample parent documents found:")
        for parent in parent_docs_found[:3]:  # Show first 3
            print(f"        - {parent['name']} (Type: {parent['additionalType']})")


def test_direct_parent_child_lookup():
    """Test the parent-child relationship by looking for specific examples"""
    
    print("\n🔎 Testing Direct Parent-Child Lookup")
    print("=" * 40)
    
    try:
        # First, find some child documents directly from the database
        print("   🔍 Looking for child documents with Aggregation types...")
        
        # Search for documents with additionalType ending in 'Aggregation'
        response = requests.get(
            f"{API_BASE_URL}/api/discovery/search",
            params={
                "contentType": "TimeSeriesAggregation",
                "pageSize": 5
            },
            timeout=30
        )
        
        if response.status_code == 200:
            results = response.json()
            records = results if isinstance(results, list) else results.get("results", [])
            
            print(f"   📋 Found {len(records)} records")
            
            # Look for parent-child relationships
            for record in records:
                name = record.get("name", "Unknown")
                additional_type = record.get("additionalType", "")
                is_part_of = record.get("isPartOf", [])
                identifier = record.get("identifier", [])
                
                print(f"\n   📄 Record: {name}")
                print(f"      Type: {additional_type}")
                print(f"      IsPartOf: {is_part_of}")
                print(f"      Identifier: {identifier}")
                
                # Check if this should be a child document
                if additional_type.endswith("Aggregation") and is_part_of:
                    print(f"      ⚠️  This appears to be a child document that should have been replaced!")
                elif not additional_type.endswith("Aggregation"):
                    print(f"      ✅ This appears to be a parent document (correct)")
        
        else:
            print(f"   ❌ Failed to get records: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Test failed: {e}")


if __name__ == "__main__":
    print("🚀 Starting Parent-Child Relationship Tests")
    print("Make sure your API server is running at", API_BASE_URL)
    print()
    
    # Run the tests
    test_parent_child_relationship_handling()
    test_direct_parent_child_lookup()
    
    print("\n✅ Tests completed!")
    print("\n💡 Key points:")
    print("   - Child documents (additionalType ending with 'Aggregation') should be replaced with parents")
    print("   - Parent documents are identified by matching identifier field with child's isPartOf field")
    print("   - If you see child documents in results, the replacement logic may need adjustment")
